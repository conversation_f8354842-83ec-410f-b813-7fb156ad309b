<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة التحكم الإدارية{% endblock %} - نظام إدارة مخازن المحروقات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .badge {
            padding: 8px 12px;
            border-radius: 20px;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 12px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-cogs"></i>
                            لوحة التحكم الإدارية
                        </h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" 
                               href="{% url 'custom_admin:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'user' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'custom_admin:user_list' %}">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'category' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'custom_admin:category_list' %}">
                                <i class="fas fa-tags"></i>
                                إدارة الأصناف
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'station' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'custom_admin:station_list' %}">
                                <i class="fas fa-gas-pump"></i>
                                إدارة المحطات
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'supplier' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'custom_admin:supplier_list' %}">
                                <i class="fas fa-truck"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'beneficiary' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'custom_admin:beneficiary_list' %}">
                                <i class="fas fa-users-cog"></i>
                                إدارة المستفيدين
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'storage' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'custom_admin:storage_list' %}">
                                <i class="fas fa-warehouse"></i>
                                إدارة المخازن
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'operation' in request.resolver_match.url_name %}active{% endif %}" 
                               href="#" data-bs-toggle="collapse" data-bs-target="#operationsMenu">
                                <i class="fas fa-exchange-alt"></i>
                                إدارة العمليات
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="operationsMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{% url 'custom_admin:incoming_operation_list' %}">
                                            <i class="fas fa-arrow-down"></i>
                                            عمليات الوارد
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{% url 'custom_admin:outgoing_operation_list' %}">
                                            <i class="fas fa-arrow-up"></i>
                                            عمليات الصادر
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{% url 'custom_admin:damage_operation_list' %}">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            عمليات التلف
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{% url 'custom_admin:storage_transfer_list' %}">
                                            <i class="fas fa-exchange-alt"></i>
                                            النقل المخزني
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:dashboard' %}">
                                <i class="fas fa-arrow-left"></i>
                                العودة للنظام الرئيسي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Breadcrumb -->
                {% block breadcrumb %}
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'custom_admin:dashboard' %}">
                                <i class="fas fa-home"></i>
                                الرئيسية
                            </a>
                        </li>
                        {% block breadcrumb_items %}{% endblock %}
                    </ol>
                </nav>
                {% endblock %}
                
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // تأكيد الحذف
        function confirmDelete(url, itemName) {
            if (confirm(`هل أنت متأكد من حذف "${itemName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        }
        
        // تبديل الحالة
        function toggleStatus(url) {
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء تغيير الحالة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تغيير الحالة');
            });
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
