from django.urls import path
from . import admin_views

app_name = 'custom_admin'

urlpatterns = [
    # لوحة التحكم الإدارية الرئيسية
    path('', admin_views.admin_dashboard, name='dashboard'),
    
    # ================================
    # إدارة المستخدمين
    # ================================
    path('users/', admin_views.user_list, name='user_list'),
    path('users/create/', admin_views.user_create, name='user_create'),
    path('users/<int:user_id>/', admin_views.user_detail, name='user_detail'),
    path('users/<int:user_id>/update/', admin_views.user_update, name='user_update'),
    path('users/<int:user_id>/change-password/', admin_views.user_change_password, name='user_change_password'),
    path('users/<int:user_id>/toggle-status/', admin_views.user_toggle_status, name='user_toggle_status'),
    path('users/<int:user_id>/delete/', admin_views.user_delete, name='user_delete'),
    
    # ================================
    # إدارة الأصناف
    # ================================
    path('categories/', admin_views.category_list, name='category_list'),
    path('categories/create/', admin_views.category_create, name='category_create'),
    path('categories/<int:category_id>/', admin_views.category_detail, name='category_detail'),
    path('categories/<int:category_id>/update/', admin_views.category_update, name='category_update'),
    path('categories/<int:category_id>/toggle-status/', admin_views.category_toggle_status, name='category_toggle_status'),
    path('categories/<int:category_id>/delete/', admin_views.category_delete, name='category_delete'),
    
    # ================================
    # إدارة المحطات
    # ================================
    path('stations/', admin_views.station_list, name='station_list'),
    path('stations/create/', admin_views.station_create, name='station_create'),
    path('stations/<int:station_id>/', admin_views.station_detail, name='station_detail'),
    path('stations/<int:station_id>/update/', admin_views.station_update, name='station_update'),
    path('stations/<int:station_id>/toggle-status/', admin_views.station_toggle_status, name='station_toggle_status'),
    path('stations/<int:station_id>/delete/', admin_views.station_delete, name='station_delete'),
    
    # ================================
    # إدارة الموردين
    # ================================
    path('suppliers/', admin_views.supplier_list, name='supplier_list'),
    path('suppliers/create/', admin_views.supplier_create, name='supplier_create'),
    path('suppliers/<int:supplier_id>/', admin_views.supplier_detail, name='supplier_detail'),
    path('suppliers/<int:supplier_id>/update/', admin_views.supplier_update, name='supplier_update'),
    path('suppliers/<int:supplier_id>/toggle-status/', admin_views.supplier_toggle_status, name='supplier_toggle_status'),
    path('suppliers/<int:supplier_id>/delete/', admin_views.supplier_delete, name='supplier_delete'),
    
    # ================================
    # إدارة المستفيدين
    # ================================
    path('beneficiaries/', admin_views.beneficiary_list, name='beneficiary_list'),
    path('beneficiaries/create/', admin_views.beneficiary_create, name='beneficiary_create'),
    path('beneficiaries/<int:beneficiary_id>/', admin_views.beneficiary_detail, name='beneficiary_detail'),
    path('beneficiaries/<int:beneficiary_id>/update/', admin_views.beneficiary_update, name='beneficiary_update'),
    path('beneficiaries/<int:beneficiary_id>/toggle-status/', admin_views.beneficiary_toggle_status, name='beneficiary_toggle_status'),
    path('beneficiaries/<int:beneficiary_id>/delete/', admin_views.beneficiary_delete, name='beneficiary_delete'),
    
    # ================================
    # إدارة المخازن
    # ================================
    path('storages/', admin_views.storage_list, name='storage_list'),
    path('storages/create/', admin_views.storage_create, name='storage_create'),
    path('storages/<int:storage_id>/', admin_views.storage_detail, name='storage_detail'),
    path('storages/<int:storage_id>/update/', admin_views.storage_update, name='storage_update'),
    path('storages/<int:storage_id>/toggle-status/', admin_views.storage_toggle_status, name='storage_toggle_status'),
    path('storages/<int:storage_id>/delete/', admin_views.storage_delete, name='storage_delete'),
    
    # ================================
    # إدارة أصناف المخازن
    # ================================
    path('storage-items/', admin_views.storage_item_list, name='storage_item_list'),
    path('storage-items/create/', admin_views.storage_item_create, name='storage_item_create'),
    path('storage-items/<int:item_id>/', admin_views.storage_item_detail, name='storage_item_detail'),
    path('storage-items/<int:item_id>/update/', admin_views.storage_item_update, name='storage_item_update'),
    path('storage-items/<int:item_id>/delete/', admin_views.storage_item_delete, name='storage_item_delete'),
    
    # ================================
    # إدارة العمليات
    # ================================
    
    # عمليات الوارد
    path('incoming-operations/', admin_views.incoming_operation_list, name='incoming_operation_list'),
    path('incoming-operations/create/', admin_views.incoming_operation_create, name='incoming_operation_create'),
    path('incoming-operations/<int:operation_id>/', admin_views.incoming_operation_detail, name='incoming_operation_detail'),
    path('incoming-operations/<int:operation_id>/update/', admin_views.incoming_operation_update, name='incoming_operation_update'),
    path('incoming-operations/<int:operation_id>/delete/', admin_views.incoming_operation_delete, name='incoming_operation_delete'),
    path('incoming-operations/<int:operation_id>/lock/', admin_views.incoming_operation_lock, name='incoming_operation_lock'),
    
    # عمليات الصادر
    path('outgoing-operations/', admin_views.outgoing_operation_list, name='outgoing_operation_list'),
    path('outgoing-operations/create/', admin_views.outgoing_operation_create, name='outgoing_operation_create'),
    path('outgoing-operations/<int:operation_id>/', admin_views.outgoing_operation_detail, name='outgoing_operation_detail'),
    path('outgoing-operations/<int:operation_id>/update/', admin_views.outgoing_operation_update, name='outgoing_operation_update'),
    path('outgoing-operations/<int:operation_id>/delete/', admin_views.outgoing_operation_delete, name='outgoing_operation_delete'),
    path('outgoing-operations/<int:operation_id>/lock/', admin_views.outgoing_operation_lock, name='outgoing_operation_lock'),
    
    # عمليات التلف
    path('damage-operations/', admin_views.damage_operation_list, name='damage_operation_list'),
    path('damage-operations/create/', admin_views.damage_operation_create, name='damage_operation_create'),
    path('damage-operations/<int:operation_id>/', admin_views.damage_operation_detail, name='damage_operation_detail'),
    path('damage-operations/<int:operation_id>/update/', admin_views.damage_operation_update, name='damage_operation_update'),
    path('damage-operations/<int:operation_id>/delete/', admin_views.damage_operation_delete, name='damage_operation_delete'),
    path('damage-operations/<int:operation_id>/lock/', admin_views.damage_operation_lock, name='damage_operation_lock'),
    
    # عمليات النقل المخزني
    path('storage-transfers/', admin_views.storage_transfer_list, name='storage_transfer_list'),
    path('storage-transfers/create/', admin_views.storage_transfer_create, name='storage_transfer_create'),
    path('storage-transfers/<int:transfer_id>/', admin_views.storage_transfer_detail, name='storage_transfer_detail'),
    path('storage-transfers/<int:transfer_id>/update/', admin_views.storage_transfer_update, name='storage_transfer_update'),
    path('storage-transfers/<int:transfer_id>/delete/', admin_views.storage_transfer_delete, name='storage_transfer_delete'),
    path('storage-transfers/<int:transfer_id>/lock/', admin_views.storage_transfer_lock, name='storage_transfer_lock'),
    
    # ================================
    # إدارة المرتجعات
    # ================================
    
    # مرتجعات الوارد
    path('incoming-returns/', admin_views.incoming_return_list, name='incoming_return_list'),
    path('incoming-returns/create/', admin_views.incoming_return_create, name='incoming_return_create'),
    path('incoming-returns/<int:return_id>/', admin_views.incoming_return_detail, name='incoming_return_detail'),
    path('incoming-returns/<int:return_id>/update/', admin_views.incoming_return_update, name='incoming_return_update'),
    path('incoming-returns/<int:return_id>/delete/', admin_views.incoming_return_delete, name='incoming_return_delete'),
    path('incoming-returns/<int:return_id>/lock/', admin_views.incoming_return_lock, name='incoming_return_lock'),
    
    # مرتجعات الصادر
    path('outgoing-returns/', admin_views.outgoing_return_list, name='outgoing_return_list'),
    path('outgoing-returns/create/', admin_views.outgoing_return_create, name='outgoing_return_create'),
    path('outgoing-returns/<int:return_id>/', admin_views.outgoing_return_detail, name='outgoing_return_detail'),
    path('outgoing-returns/<int:return_id>/update/', admin_views.outgoing_return_update, name='outgoing_return_update'),
    path('outgoing-returns/<int:return_id>/delete/', admin_views.outgoing_return_delete, name='outgoing_return_delete'),
    path('outgoing-returns/<int:return_id>/lock/', admin_views.outgoing_return_lock, name='outgoing_return_lock'),
    
    # ================================
    # إدارة الصيانة ومراقبة الجودة
    # ================================
    
    # جدولة الصيانة
    path('maintenance/', admin_views.maintenance_list, name='maintenance_list'),
    path('maintenance/create/', admin_views.maintenance_create, name='maintenance_create'),
    path('maintenance/<int:maintenance_id>/', admin_views.maintenance_detail, name='maintenance_detail'),
    path('maintenance/<int:maintenance_id>/update/', admin_views.maintenance_update, name='maintenance_update'),
    path('maintenance/<int:maintenance_id>/delete/', admin_views.maintenance_delete, name='maintenance_delete'),
    path('maintenance/<int:maintenance_id>/complete/', admin_views.maintenance_complete, name='maintenance_complete'),
    
    # مراقبة الجودة
    path('quality-control/', admin_views.quality_control_list, name='quality_control_list'),
    path('quality-control/create/', admin_views.quality_control_create, name='quality_control_create'),
    path('quality-control/<int:qc_id>/', admin_views.quality_control_detail, name='quality_control_detail'),
    path('quality-control/<int:qc_id>/update/', admin_views.quality_control_update, name='quality_control_update'),
    path('quality-control/<int:qc_id>/delete/', admin_views.quality_control_delete, name='quality_control_delete'),
]
