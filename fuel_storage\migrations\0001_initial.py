# Generated by Django 4.2.7 on 2025-08-07 03:21

import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Beneficiary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('beneficiary_type', models.CharField(max_length=50, verbose_name='نوع المستفيد')),
                ('phone_number', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('contact_person', models.CharField(max_length=100, verbose_name='الشخص المسؤول')),
                ('credit_limit', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='حد الائتمان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
            ],
            options={
                'verbose_name': 'المستفيد',
                'verbose_name_plural': 'المستفيدين',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('minimum_stock_level', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock_level', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='الحد الأعلى للمخزون')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
            ],
            options={
                'verbose_name': 'الصنف',
                'verbose_name_plural': 'الأصناف',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='IncomingOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
                ('is_locked', models.BooleanField(default=False, verbose_name='محظور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
            ],
            options={
                'verbose_name': 'عملية وارد',
                'verbose_name_plural': 'عمليات الوارد',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
                ('is_locked', models.BooleanField(default=False, verbose_name='محظور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('beneficiary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.beneficiary', verbose_name='المستفيد')),
            ],
            options={
                'verbose_name': 'عملية صادر',
                'verbose_name_plural': 'عمليات الصادر',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Station',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('manager_name', models.CharField(max_length=100, verbose_name='اسم المدير')),
                ('phone_number', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
            ],
            options={
                'verbose_name': 'المحطة',
                'verbose_name_plural': 'المحطات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Storage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('classification', models.CharField(max_length=50, verbose_name='التصنيف')),
                ('location', models.TextField(verbose_name='الموقع')),
                ('capacity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعة')),
                ('current_level', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية الحالية')),
                ('keeper_name', models.CharField(max_length=100, verbose_name='اسم أمين المخزن')),
                ('phone_number', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('temperature_controlled', models.BooleanField(default=False, verbose_name='متحكم درجة الحرارة')),
                ('security_level', models.CharField(max_length=20, verbose_name='مستوى الأمن')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('station', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.station', verbose_name='المحطة')),
            ],
            options={
                'verbose_name': 'المخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('supplier_type', models.CharField(max_length=50, verbose_name='نوع المورد')),
                ('phone_number', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('credit_limit', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='حد الائتمان')),
                ('payment_terms', models.CharField(max_length=100, verbose_name='شروط الدفع')),
                ('rating', models.CharField(max_length=20, verbose_name='التقييم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
            ],
            options={
                'verbose_name': 'المورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StorageTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('is_locked', models.BooleanField(default=False, verbose_name='محظور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.category', verbose_name='الصنف')),
                ('from_storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='fuel_storage.storage', verbose_name='من المخزن')),
                ('to_storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='fuel_storage.storage', verbose_name='إلى المخزن')),
            ],
            options={
                'verbose_name': 'تحويل بين المخازن',
                'verbose_name_plural': 'تحويلات بين المخازن',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StorageItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.category', verbose_name='الصنف')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'عنصر المخزن',
                'verbose_name_plural': 'عناصر المخازن',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QualityControl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inspection_date', models.DateTimeField(verbose_name='تاريخ الفحص')),
                ('status', models.CharField(choices=[('passed', 'اجتاز'), ('failed', 'فشل'), ('pending', 'قيد الفحص')], default='pending', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('storage_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storageitem', verbose_name='عنصر المخزن')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'الفحوصات الجودة',
                'ordering': ['-inspection_date'],
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('reason', models.TextField(verbose_name='سبب الإرجاع')),
                ('is_locked', models.BooleanField(default=False, verbose_name='محظور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
            ],
            options={
                'verbose_name': 'إرجاع صادر',
                'verbose_name_plural': 'إرجاعات الصادر',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='outgoingoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='MaintenanceSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateTimeField(verbose_name='تاريخ الجدولة')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('is_completed', models.BooleanField(default=False, verbose_name='منجز')),
                ('completed_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'جدولة صيانة',
                'verbose_name_plural': 'جدولة الصيانة',
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='IncomingReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('reason', models.TextField(verbose_name='سبب الإرجاع')),
                ('is_locked', models.BooleanField(default=False, verbose_name='محظور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
            ],
            options={
                'verbose_name': 'إرجاع وارد',
                'verbose_name_plural': 'إرجاعات الوارد',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.supplier', verbose_name='المورد'),
        ),
        migrations.CreateModel(
            name='DamageOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('reason', models.TextField(verbose_name='سبب الأضرار')),
                ('is_locked', models.BooleanField(default=False, verbose_name='محظور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.category', verbose_name='الصنف')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'عملية أضرار',
                'verbose_name_plural': 'عمليات الأضرار',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('user_type', models.CharField(choices=[('manager', 'مدير'), ('operator', 'مشغل')], default='operator', max_length=20)),
                ('full_name', models.CharField(max_length=100)),
                ('groups', models.ManyToManyField(blank=True, help_text='المجموعات التي ينتمي إليها هذا المستخدم.', related_name='customuser_set', related_query_name='customuser', to='auth.group', verbose_name='المجموعات')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='صلاحيات محددة لهذا المستخدم.', related_name='customuser_set', related_query_name='customuser', to='auth.permission', verbose_name='صلاحيات المستخدم')),
            ],
            options={
                'verbose_name': 'المستخدم',
                'verbose_name_plural': 'المستخدمين',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
