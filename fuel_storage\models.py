from django.db import models
from django.contrib.auth.models import AbstractUser

# Custom User Model
class CustomUser(AbstractUser):
    USER_TYPES = [
        ('manager', 'مدير'),
        ('operator', 'مشغل'),
    ]
    
    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='operator')
    full_name = models.CharField(max_length=100)
    
    class Meta:
        verbose_name = 'المستخدم'
        verbose_name_plural = 'المستخدمين'

    # Override the related_name for groups and user_permissions
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='المجموعات',
        blank=True,
        help_text='المجموعات التي ينتمي إليها هذا المستخدم.',
        related_name='customuser_set',
        related_query_name='customuser'
    )

    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='صلاحيات المستخدم',
        blank=True,
        help_text='صلاحيات محددة لهذا المستخدم.',
        related_name='customuser_set',
        related_query_name='customuser'
    )

# Category Model
class Category(models.Model):
    name = models.CharField(max_length=100, verbose_name='الاسم')
    code = models.CharField(max_length=50, verbose_name='الرمز', unique=True)
    description = models.TextField(verbose_name='الوصف', blank=True, null=True)
    minimum_stock_level = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='الحد الأدنى للمخزون')
    maximum_stock_level = models.DecimalField(max_digits=10, decimal_places=3, verbose_name='الحد الأعلى للمخزون')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'الصنف'
        verbose_name_plural = 'الأصناف'
        ordering = ['-created_at']

# Station Model
class Station(models.Model):
    name = models.CharField(max_length=100, verbose_name='الاسم')
    code = models.CharField(max_length=50, verbose_name='الرمز', unique=True)
    address = models.TextField(verbose_name='العنوان')
    manager_name = models.CharField(max_length=100, verbose_name='اسم المدير')
    phone_number = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'المحطة'
        verbose_name_plural = 'المحطات'
        ordering = ['-created_at']

# Supplier Model
class Supplier(models.Model):
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    code = models.CharField(max_length=50, verbose_name='الرمز', unique=True)
    supplier_type = models.CharField(max_length=50, verbose_name='نوع المورد')
    phone_number = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(verbose_name='البريد الإلكتروني')
    address = models.TextField(verbose_name='العنوان')
    tax_number = models.CharField(max_length=50, verbose_name='الرقم الضريبي', blank=True, null=True)
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='حد الائتمان')
    payment_terms = models.CharField(max_length=100, verbose_name='شروط الدفع')
    rating = models.CharField(max_length=20, verbose_name='التقييم')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'المورد'
        verbose_name_plural = 'الموردين'
        ordering = ['-created_at']

# Beneficiary Model
class Beneficiary(models.Model):
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    code = models.CharField(max_length=50, verbose_name='الرمز', unique=True)
    beneficiary_type = models.CharField(max_length=50, verbose_name='نوع المستفيد')
    phone_number = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(verbose_name='البريد الإلكتروني')
    address = models.TextField(verbose_name='العنوان')
    contact_person = models.CharField(max_length=100, verbose_name='الشخص المسؤول')
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='حد الائتمان')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'المستفيد'
        verbose_name_plural = 'المستفيدين'
        ordering = ['-created_at']

# Storage Model
class Storage(models.Model):
    name = models.CharField(max_length=100, verbose_name='الاسم')
    code = models.CharField(max_length=50, verbose_name='الرمز', unique=True)
    station = models.ForeignKey(Station, on_delete=models.CASCADE, verbose_name='المحطة')
    classification = models.CharField(max_length=50, verbose_name='التصنيف')
    location = models.TextField(verbose_name='الموقع')
    capacity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعة')
    current_level = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية الحالية')
    keeper_name = models.CharField(max_length=100, verbose_name='اسم أمين المخزن')
    phone_number = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(verbose_name='البريد الإلكتروني')
    temperature_controlled = models.BooleanField(default=False, verbose_name='متحكم درجة الحرارة')
    security_level = models.CharField(max_length=20, verbose_name='مستوى الأمن')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'المخزن'
        verbose_name_plural = 'المخازن'
        ordering = ['-created_at']

# StorageItem Model
class StorageItem(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='الصنف')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='سعر الوحدة')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر الإجمالي')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'عنصر المخزن'
        verbose_name_plural = 'عناصر المخازن'
        ordering = ['-created_at']

# Operation Models
class IncomingOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name='المورد')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='سعر الوحدة')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر الإجمالي')
    is_locked = models.BooleanField(default=False, verbose_name='محظور')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'عملية وارد'
        verbose_name_plural = 'عمليات الوارد'
        ordering = ['-created_at']

# Outgoing Operation
class OutgoingOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    beneficiary = models.ForeignKey(Beneficiary, on_delete=models.CASCADE, verbose_name='المستفيد')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='سعر الوحدة')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر الإجمالي')
    is_locked = models.BooleanField(default=False, verbose_name='محظور')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'عملية صادر'
        verbose_name_plural = 'عمليات الصادر'
        ordering = ['-created_at']

# Damage Operation
class DamageOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='الصنف')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    reason = models.TextField(verbose_name='سبب الأضرار')
    is_locked = models.BooleanField(default=False, verbose_name='محظور')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'عملية أضرار'
        verbose_name_plural = 'عمليات الأضرار'
        ordering = ['-created_at']

# Storage Transfer
class StorageTransfer(models.Model):
    from_storage = models.ForeignKey(Storage, on_delete=models.CASCADE, related_name='transfers_from', verbose_name='من المخزن')
    to_storage = models.ForeignKey(Storage, on_delete=models.CASCADE, related_name='transfers_to', verbose_name='إلى المخزن')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='الصنف')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    is_locked = models.BooleanField(default=False, verbose_name='محظور')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'تحويل بين المخازن'
        verbose_name_plural = 'تحويلات بين المخازن'
        ordering = ['-created_at']

# Return Models
class IncomingReturn(models.Model):
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.CASCADE, verbose_name='عملية الوارد')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    reason = models.TextField(verbose_name='سبب الإرجاع')
    is_locked = models.BooleanField(default=False, verbose_name='محظور')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'إرجاع وارد'
        verbose_name_plural = 'إرجاعات الوارد'
        ordering = ['-created_at']

# Outgoing Return
class OutgoingReturn(models.Model):
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.CASCADE, verbose_name='عملية الصادر')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='الكمية')
    reason = models.TextField(verbose_name='سبب الإرجاع')
    is_locked = models.BooleanField(default=False, verbose_name='محظور')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'إرجاع صادر'
        verbose_name_plural = 'إرجاعات الصادر'
        ordering = ['-created_at']

# Maintenance Schedule
class MaintenanceSchedule(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    scheduled_date = models.DateTimeField(verbose_name='تاريخ الجدولة')
    description = models.TextField(verbose_name='الوصف')
    is_completed = models.BooleanField(default=False, verbose_name='منجز')
    completed_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الإنجاز')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'جدولة صيانة'
        verbose_name_plural = 'جدولة الصيانة'
        ordering = ['scheduled_date']

# Quality Control
class QualityControl(models.Model):
    storage_item = models.ForeignKey(StorageItem, on_delete=models.CASCADE, verbose_name='عنصر المخزن')
    inspection_date = models.DateTimeField(verbose_name='تاريخ الفحص')
    status = models.CharField(max_length=20, choices=[
        ('passed', 'اجتاز'),
        ('failed', 'فشل'),
        ('pending', 'قيد الفحص')
    ], default='pending', verbose_name='الحالة')
    notes = models.TextField(verbose_name='ملاحظات', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التعديل')
    
    class Meta:
        verbose_name = 'فحص جودة'
        verbose_name_plural = 'الفحوصات الجودة'
        ordering = ['-inspection_date']
